#!/bin/bash

# A script to set up the complete build environment for the Claudia application on a fresh Ubuntu system.
# This script installs system dependencies, Rust, Bun, and the Tauri CLI.

# Exit immediately if a command exits with a non-zero status.
set -e

echo "🚀 Starting the setup for the Claudia build environment on Ubuntu..."
echo "This will install all necessary system packages, Rust, Bun, and the Tauri CLI."

# --- 1. Update and Install System Dependencies ---
echo ""
echo "--> Step 1: Updating package lists and installing system dependencies..."
sudo apt-get update
sudo apt-get install -y \
    build-essential \
    curl \
    wget \
    file \
    git \
    libwebkit2gtk-4.1-dev \
    libgtk-3-dev \
    libayatana-appindicator3-dev \
    librsvg2-dev \
    patchelf \
    libssl-dev \
    libxdo-dev \
    libsoup-3.0-dev \
    libjavascriptcoregtk-4.1-dev \
    libfuse2 \
    libgl1-mesa-glx

echo "✅ System dependencies installed successfully."

# --- 2. Install Rust and Cargo ---
echo ""
if ! command -v cargo &> /dev/null
then
    echo "--> Step 2: Rust not found. Installing Rust and Cargo..."
    curl --proto '=https' --tlsv1.2 -sSf https://sh.rustup.rs | sh -s -- -y
    # Add cargo to the current shell's PATH for subsequent steps in this script
    source "$HOME/.cargo/env"
    echo "✅ Rust installed successfully."
else
    echo "--> Step 2: Rust is already installed. Skipping."
fi

# --- 3. Install Bun ---
echo ""
# Source the bun env file if it exists, in case bun is installed but not in PATH
if [ -f "$HOME/.bun/env" ]; then
    source "$HOME/.bun/env"
fi

if ! command -v bun &> /dev/null
then
    echo "--> Step 3: Bun not found. Installing Bun..."
    curl -fsSL https://bun.sh/install | bash
    # Add bun to the current shell's PATH for subsequent steps in this script
    source "$HOME/.bun/env"
    echo "✅ Bun installed successfully."
else
    echo "--> Step 3: Bun is already installed. Skipping."
fi

# --- 4. Install Tauri CLI ---
echo ""
# Ensure Cargo's bin directory is in the PATH for this check
export PATH="$HOME/.cargo/bin:$PATH"
if ! command -v cargo-tauri &> /dev/null
then
    echo "--> Step 4: Tauri CLI not found. Installing via Cargo..."
    cargo install tauri-cli
    echo "✅ Tauri CLI installed successfully."
else
    echo "--> Step 4: Tauri CLI is already installed. Skipping."
fi


# --- 5. Final Instructions ---
echo ""
echo "🎉 Environment setup is complete!"
echo ""
echo "IMPORTANT: To make the 'bun' and 'cargo' commands available in your terminal,"
echo "you must first reload your shell's configuration."
echo ""
echo "Please do one of the following:"
echo "1. Close and reopen your terminal."
echo "OR"
echo "2. Run the following command:"
echo '   source "$HOME/.cargo/env" && source "$HOME/.bun/env"'
echo ""
echo "After that, you can clone the Claudia repository and run 'bun run tauri build' to build the project."
